import React, { useState, useEffect } from "react";
import { X, Save, Loader2, Eye, EyeOff, RefreshCw } from "lucide-react";
import { Merchandizer } from "../../firebase/services";
import { Timestamp } from "firebase/firestore";
import { useApp } from "../../hooks/useApp";
import {
  MobilePermission,
  generateTemporaryPassword,
  getDefaultMerchandizerPermissions,
  isValidUsername,
} from "../../models";

interface MerchandizerModalProps {
  isOpen: boolean;
  onClose: () => void;
  merchandizer?: Merchandizer | null;
  mode: "create" | "edit" | "view";
}

const MerchandizerModal: React.FC<MerchandizerModalProps> = ({
  isOpen,
  onClose,
  merchandizer,
  mode,
}) => {
  const { addMerchandizer, updateMerchandizer } = useApp();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    territory: "",
    stores: 0,
    lastVisit: new Date().toISOString().split("T")[0],
    performance: 0,
    status: "active" as "active" | "inactive",

    // Account fields
    username: "",
    temporaryPassword: "",
    permissions: getDefaultMerchandizerPermissions(),
    canAccessOffline: true,
    maxOfflineDays: 7,
    requireBiometric: false,
    sessionTimeout: 480,
    allowMultipleDevices: false,
  });

  const [showPassword, setShowPassword] = useState(false);

  // Generate username from name
  const generateUsername = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]/g, "")
      .substring(0, 15);
  };

  // Generate new temporary password
  const handleGeneratePassword = () => {
    const newPassword = generateTemporaryPassword();
    setFormData((prev) => ({ ...prev, temporaryPassword: newPassword }));
  };

  const territories = [
    "Centre-ville",
    "Quartier Nord",
    "Quartier Sud",
    "Quartier Est",
    "Quartier Ouest",
    "Banlieue Nord",
    "Banlieue Sud",
    "Zone Commerciale",
    "Centres Commerciaux",
    "Zone Industrielle",
  ];

  useEffect(() => {
    if (merchandizer && (mode === "edit" || mode === "view")) {
      setFormData({
        name: merchandizer.name || "",
        email: merchandizer.email || "",
        phone: merchandizer.phone || "",
        territory: merchandizer.territory || "",
        stores: merchandizer.stores || 0,
        lastVisit: merchandizer.lastVisit
          ? new Date(merchandizer.lastVisit.toDate())
              .toISOString()
              .split("T")[0]
          : "",
        performance: merchandizer.performance || 0,
        status: merchandizer.status || "active",
      });
    } else {
      const tempPassword = generateTemporaryPassword();
      setFormData({
        name: "",
        email: "",
        phone: "",
        territory: "",
        stores: 0,
        lastVisit: new Date().toISOString().split("T")[0],
        performance: 0,
        status: "active",

        // Account fields for new merchandizer
        username: "",
        temporaryPassword: tempPassword,
        permissions: getDefaultMerchandizerPermissions(),
        canAccessOffline: true,
        maxOfflineDays: 7,
        requireBiometric: false,
        sessionTimeout: 480,
        allowMultipleDevices: false,
      });
    }
    setError(null);
  }, [merchandizer, mode, isOpen]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value, type } = e.target;
    const newValue = type === "number" ? parseFloat(value) || 0 : value;

    setFormData((prev) => {
      const updated = {
        ...prev,
        [name]: newValue,
      };

      // Auto-generate username when name changes (only for create mode)
      if (name === "name" && mode === "create" && value) {
        updated.username = generateUsername(value);
      }

      return updated;
    });
  };

  const validateForm = () => {
    if (!formData.name.trim()) {
      setError("Le nom est requis");
      return false;
    }
    if (!formData.email.trim()) {
      setError("L'email est requis");
      return false;
    }
    if (!formData.email.includes("@")) {
      setError("L'email doit être valide");
      return false;
    }
    if (!formData.phone.trim()) {
      setError("Le téléphone est requis");
      return false;
    }
    if (!formData.territory.trim()) {
      setError("Le territoire est requis");
      return false;
    }
    if (formData.stores < 0) {
      setError("Le nombre de magasins ne peut pas être négatif");
      return false;
    }
    if (formData.performance < 0 || formData.performance > 100) {
      setError("La performance doit être entre 0 et 100");
      return false;
    }

    // Account validation for create mode
    if (mode === "create") {
      if (!formData.username.trim()) {
        setError("Le nom d'utilisateur est requis");
        return false;
      }
      if (!isValidUsername(formData.username)) {
        setError(
          "Le nom d'utilisateur doit contenir uniquement des lettres, chiffres et underscore (3-20 caractères)"
        );
        return false;
      }
      if (!formData.temporaryPassword.trim()) {
        setError("Le mot de passe temporaire est requis");
        return false;
      }
      if (formData.temporaryPassword.length < 6) {
        setError("Le mot de passe doit contenir au moins 6 caractères");
        return false;
      }
      if (formData.permissions.length === 0) {
        setError("Au moins une permission est requise");
        return false;
      }
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (mode === "view") return;

    if (!validateForm()) return;

    setLoading(true);
    setError(null);

    try {
      const merchandizerData = {
        firstName: formData.name.split(" ")[0] || formData.name,
        lastName: formData.name.split(" ").slice(1).join(" ") || "",
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        mobile: formData.phone, // Using phone as mobile for now
        employeeId: `MERCH-${Date.now()}`,
        type: "internal",
        department: "Merchandising",
        territory: formData.territory,
        stores: formData.stores,
        lastVisit: Timestamp.fromDate(new Date(formData.lastVisit)),
        performance: formData.performance,
        status: formData.status,

        // Account information for mobile app
        account: {
          username: formData.username,
          email: formData.email,
          temporaryPassword:
            mode === "create" ? formData.temporaryPassword : undefined,
          accountStatus: "pending_activation",
          isFirstLogin: true,
          mustChangePassword: true,
          permissions: formData.permissions,
          canAccessOffline: formData.canAccessOffline,
          maxOfflineDays: formData.maxOfflineDays,
          requireBiometric: formData.requireBiometric,
          sessionTimeout: formData.sessionTimeout,
          allowMultipleDevices: formData.allowMultipleDevices,
          createdBy: "admin", // TODO: Get actual admin user
          createdDate: Timestamp.now(),
        },
      };

      if (mode === "create") {
        await addMerchandizer(merchandizerData);
      } else if (mode === "edit" && merchandizer?.id) {
        // For edit mode, don't include password in update
        const updateData = { ...merchandizerData };
        if (updateData.account.temporaryPassword) {
          delete updateData.account.temporaryPassword;
        }
        await updateMerchandizer(merchandizer.id, updateData);
      }

      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : "Une erreur est survenue");
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  const isReadOnly = mode === "view";
  const title =
    mode === "create"
      ? "Nouveau Merchandiseur"
      : mode === "edit"
      ? "Modifier Merchandiseur"
      : "Détails Merchandiseur";

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form
          onSubmit={handleSubmit}
          className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]"
        >
          {error && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {error}
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label
                htmlFor="name"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Nom *
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                disabled={isReadOnly}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                required
              />
            </div>

            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Email *
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                disabled={isReadOnly}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                required
              />
            </div>

            <div>
              <label
                htmlFor="phone"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Téléphone *
              </label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                disabled={isReadOnly}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                required
              />
            </div>

            <div>
              <label
                htmlFor="territory"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Territoire *
              </label>
              <select
                id="territory"
                name="territory"
                value={formData.territory}
                onChange={handleInputChange}
                disabled={isReadOnly}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                required
              >
                <option value="">Sélectionner un territoire</option>
                {territories.map((territory) => (
                  <option key={territory} value={territory}>
                    {territory}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label
                htmlFor="stores"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Nombre de magasins
              </label>
              <input
                type="number"
                id="stores"
                name="stores"
                value={formData.stores}
                onChange={handleInputChange}
                disabled={isReadOnly}
                min="0"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
              />
            </div>

            <div>
              <label
                htmlFor="performance"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Performance (%)
              </label>
              <input
                type="number"
                id="performance"
                name="performance"
                value={formData.performance}
                onChange={handleInputChange}
                disabled={isReadOnly}
                min="0"
                max="100"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
              />
            </div>

            <div>
              <label
                htmlFor="lastVisit"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Dernière visite
              </label>
              <input
                type="date"
                id="lastVisit"
                name="lastVisit"
                value={formData.lastVisit}
                onChange={handleInputChange}
                disabled={isReadOnly}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
              />
            </div>

            {/* Account Creation Section - Only for create mode */}
            {mode === "create" && (
              <>
                <div className="col-span-2 border-t border-gray-200 pt-4 mt-4">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    Compte Application Mobile
                  </h3>
                </div>

                <div>
                  <label
                    htmlFor="username"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Nom d'utilisateur *
                  </label>
                  <input
                    type="text"
                    id="username"
                    name="username"
                    value={formData.username}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="nom.utilisateur"
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    3-20 caractères, lettres, chiffres et underscore uniquement
                  </p>
                </div>

                <div>
                  <label
                    htmlFor="temporaryPassword"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Mot de passe temporaire *
                  </label>
                  <div className="relative">
                    <input
                      type={showPassword ? "text" : "password"}
                      id="temporaryPassword"
                      name="temporaryPassword"
                      value={formData.temporaryPassword}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 pr-20 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center space-x-1 pr-3">
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="text-gray-400 hover:text-gray-600"
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </button>
                      <button
                        type="button"
                        onClick={handleGeneratePassword}
                        className="text-blue-600 hover:text-blue-800"
                        title="Générer un nouveau mot de passe"
                      >
                        <RefreshCw className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    L'utilisateur devra changer ce mot de passe lors de sa
                    première connexion
                  </p>
                </div>

                <div className="col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Permissions
                  </label>
                  <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto border border-gray-200 rounded-md p-3">
                    {Object.values(MobilePermission).map((permission) => (
                      <label
                        key={permission}
                        className="flex items-center space-x-2"
                      >
                        <input
                          type="checkbox"
                          checked={formData.permissions.includes(permission)}
                          onChange={(e) => {
                            const newPermissions = e.target.checked
                              ? [...formData.permissions, permission]
                              : formData.permissions.filter(
                                  (p) => p !== permission
                                );
                            setFormData((prev) => ({
                              ...prev,
                              permissions: newPermissions,
                            }));
                          }}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="text-sm text-gray-700">
                          {permission.replace(/_/g, " ").toLowerCase()}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={formData.canAccessOffline}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          canAccessOffline: e.target.checked,
                        }))
                      }
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">
                      Accès hors ligne autorisé
                    </span>
                  </label>
                </div>

                <div>
                  <label
                    htmlFor="maxOfflineDays"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Jours hors ligne maximum
                  </label>
                  <input
                    type="number"
                    id="maxOfflineDays"
                    name="maxOfflineDays"
                    value={formData.maxOfflineDays}
                    onChange={handleInputChange}
                    min="1"
                    max="30"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </>
            )}

            <div>
              <label
                htmlFor="status"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Statut
              </label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                disabled={isReadOnly}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
              >
                <option value="active">Actif</option>
                <option value="inactive">Inactif</option>
              </select>
            </div>
          </div>

          {!isReadOnly && (
            <div className="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
              >
                Annuler
              </button>
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center space-x-2"
              >
                {loading && <Loader2 className="h-4 w-4 animate-spin" />}
                <Save className="h-4 w-4" />
                <span>{mode === "create" ? "Créer" : "Sauvegarder"}</span>
              </button>
            </div>
          )}
        </form>
      </div>
    </div>
  );
};

export default MerchandizerModal;
