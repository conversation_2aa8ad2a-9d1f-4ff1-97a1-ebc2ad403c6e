import React, { useState, useEffect } from "react";
import {
  TrendingUp,
  Users,
  Package,
  DollarSign,
  ClipboardList,
  Crown,
} from "lucide-react";
import { useApp } from "../hooks/useApp";
import { FirebaseService, Order } from "../firebase/services";
import NetworkStatus from "../components/features/NetworkStatus";

// Types for dashboard stats
interface TopClient {
  id: string;
  name: string;
  amount: number;
  orders: number;
  percentage?: number;
}

interface DashboardStats {
  totalSales: number;
  activeClients: number;
  totalProducts: number;
  totalOrders: number;
  topClients: TopClient[];
  recentOrders: Order[];
  salesGrowth: number;
  clientsGrowth: number;
  productsGrowth: number;
  ordersGrowth: number;
}

const Dashboard: React.FC = () => {
  const { clients, orders, products, loading } = useApp();
  const [hoveredSegment, setHoveredSegment] = useState<number | null>(null);
  const [hoveredLegend, setHoveredLegend] = useState<number | null>(null);
  const [showNetworkError, setShowNetworkError] = useState(false);
  const [dashboardStats, setDashboardStats] = useState<DashboardStats>({
    totalSales: 0,
    activeClients: 0,
    totalProducts: 0,
    totalOrders: 0,
    topClients: [],
    recentOrders: [],
    salesGrowth: 0,
    clientsGrowth: 0,
    productsGrowth: 0,
    ordersGrowth: 0,
  });

  useEffect(() => {
    if (!loading && clients.length > 0) {
      loadDashboardStats();
    }
  }, [clients, orders, products, loading]);

  const loadDashboardStats = async () => {
    try {
      setShowNetworkError(false);
      const stats = await FirebaseService.getDashboardStatsWithRetry();
      setDashboardStats(stats);
    } catch (error) {
      console.error("Error loading dashboard stats:", error);
      // Show user-friendly error message
      const errorMessage =
        error instanceof Error ? error.message : "Erreur de connexion";
      if (
        errorMessage.includes("net::ERR_QUIC_PROTOCOL_ERROR") ||
        errorMessage.includes("unavailable")
      ) {
        setShowNetworkError(true);
        console.error(
          "Problème de connexion réseau. Vérifiez votre connexion internet."
        );
      }
    }
  };

  const formatGrowth = (growth: number) => {
    const sign = growth >= 0 ? "+" : "";
    return `${sign}${growth.toFixed(1)}%`;
  };

  const stats = [
    {
      title: "Ventes Totales",
      value: `${dashboardStats.totalSales.toLocaleString()} DT`,
      change: formatGrowth(dashboardStats.salesGrowth),
      icon: DollarSign,
      color: dashboardStats.salesGrowth >= 0 ? "green" : "red",
    },
    {
      title: "Clients Actifs",
      value: dashboardStats.activeClients.toString(),
      change: formatGrowth(dashboardStats.clientsGrowth),
      icon: Users,
      color: dashboardStats.clientsGrowth >= 0 ? "blue" : "red",
    },
    {
      title: "Produits",
      value: dashboardStats.totalProducts.toString(),
      change: formatGrowth(dashboardStats.productsGrowth),
      icon: Package,
      color: dashboardStats.productsGrowth >= 0 ? "purple" : "red",
    },
    {
      title: "Commandes",
      value: dashboardStats.totalOrders.toString(),
      change: formatGrowth(dashboardStats.ordersGrowth),
      icon: ClipboardList,
      color: dashboardStats.ordersGrowth >= 0 ? "orange" : "red",
    },
  ];

  const recentActivity = dashboardStats.recentOrders.map((order: Order) => ({
    action: "Nouvelle commande",
    entity: order.orderNumber,
    time: new Date(order.createdAt?.toDate()).toLocaleString(),
    type: "order",
  }));

  // Top 5 clients by order amount - use Firebase data
  const topClients = dashboardStats.topClients.map((client: TopClient) => ({
    ...client,
    percentage: Math.round((client.amount / dashboardStats.totalSales) * 100),
  }));

  const colors = [
    {
      primary: "#3B82F6",
      secondary: "#1E40AF",
      gradient: "from-blue-400 to-blue-600",
    },
    {
      primary: "#10B981",
      secondary: "#059669",
      gradient: "from-emerald-400 to-emerald-600",
    },
    {
      primary: "#F59E0B",
      secondary: "#D97706",
      gradient: "from-amber-400 to-amber-600",
    },
    {
      primary: "#EF4444",
      secondary: "#DC2626",
      gradient: "from-red-400 to-red-600",
    },
    {
      primary: "#8B5CF6",
      secondary: "#7C3AED",
      gradient: "from-violet-400 to-violet-600",
    },
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Network Status Notification */}
      <NetworkStatus
        show={showNetworkError}
        onRetry={() => loadDashboardStats()}
      />

      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Tableau de Bord</h1>
        <div className="text-sm text-gray-500">
          Dernière mise à jour: {new Date().toLocaleDateString()}
        </div>
      </div>

      {/* Welcome message if no data */}
      {!loading && clients.length === 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
          <h2 className="text-lg font-semibold text-blue-900 mb-2">
            Bienvenue dans votre tableau de bord!
          </h2>
          <p className="text-blue-700">
            Commencez par ajouter des clients, commerciaux et produits pour voir
            vos données ici.
          </p>
        </div>
      )}

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <div
              key={index}
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    {stat.title}
                  </p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">
                    {stat.value}
                  </p>
                  <p
                    className={`text-sm font-medium mt-1 ${
                      stat.color === "green"
                        ? "text-green-600"
                        : stat.color === "blue"
                        ? "text-blue-600"
                        : stat.color === "purple"
                        ? "text-purple-600"
                        : stat.color === "red"
                        ? "text-red-600"
                        : "text-orange-600"
                    }`}
                  >
                    <TrendingUp className="inline h-4 w-4 mr-1" />
                    {stat.change}
                  </p>
                </div>
                <div
                  className={`p-3 rounded-lg ${
                    stat.color === "green"
                      ? "bg-green-100"
                      : stat.color === "blue"
                      ? "bg-blue-100"
                      : stat.color === "purple"
                      ? "bg-purple-100"
                      : "bg-orange-100"
                  }`}
                >
                  <Icon
                    className={`h-6 w-6 ${
                      stat.color === "green"
                        ? "text-green-600"
                        : stat.color === "blue"
                        ? "text-blue-600"
                        : stat.color === "purple"
                        ? "text-purple-600"
                        : "text-orange-600"
                    }`}
                  />
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Charts and Activity Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top 5 Clients Chart */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-gray-100">
            <div className="flex items-center space-x-2">
              <Crown className="h-5 w-5 text-yellow-500" />
              <h2 className="text-lg font-semibold text-gray-900">
                Top 5 Clients par Commandes
              </h2>
            </div>
          </div>
          <div className="p-8">
            <div className="flex items-center justify-center mb-8">
              {/* Enhanced Circular Chart */}
              <div className="relative w-64 h-64">
                {/* Outer glow effect */}
                <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-100 to-purple-100 opacity-20 blur-xl"></div>

                {/* Main SVG Chart */}
                <svg
                  className="w-64 h-64 transform -rotate-90 drop-shadow-lg"
                  viewBox="0 0 100 100"
                >
                  {/* Background circle */}
                  <circle
                    cx="50"
                    cy="50"
                    r="22"
                    fill="transparent"
                    stroke="#f1f5f9"
                    strokeWidth="6"
                    className="opacity-20"
                  />

                  {/* Gradient definitions */}
                  <defs>
                    {colors.map((color, index) => (
                      <linearGradient
                        key={index}
                        id={`gradient-${index}`}
                        x1="0%"
                        y1="0%"
                        x2="100%"
                        y2="100%"
                      >
                        <stop offset="0%" stopColor={color.primary} />
                        <stop offset="100%" stopColor={color.secondary} />
                      </linearGradient>
                    ))}

                    {/* Drop shadow filter */}
                    <filter
                      id="drop-shadow"
                      x="-50%"
                      y="-50%"
                      width="200%"
                      height="200%"
                    >
                      <feDropShadow
                        dx="0"
                        dy="2"
                        stdDeviation="2"
                        floodColor="rgba(0,0,0,0.1)"
                      />
                    </filter>
                  </defs>

                  {/* Chart segments */}
                  {topClients.map((client, index) => {
                    const prevPercentages = topClients
                      .slice(0, index)
                      .reduce((sum, c) => sum + c.percentage, 0);
                    const circumference = 2 * Math.PI * 22;
                    const strokeDasharray = `${
                      (client.percentage / 100) * circumference
                    } ${circumference}`;
                    const strokeDashoffset = -(
                      (prevPercentages / 100) *
                      circumference
                    );
                    const isHovered =
                      hoveredSegment === index || hoveredLegend === index;

                    return (
                      <circle
                        key={index}
                        cx="50"
                        cy="50"
                        r="22"
                        fill="transparent"
                        stroke={`url(#gradient-${index})`}
                        strokeWidth={isHovered ? "8" : "6"}
                        strokeDasharray={strokeDasharray}
                        strokeDashoffset={strokeDashoffset}
                        strokeLinecap="round"
                        filter="url(#drop-shadow)"
                        className="transition-all duration-300 ease-out cursor-pointer"
                        onMouseEnter={() => setHoveredSegment(index)}
                        onMouseLeave={() => setHoveredSegment(null)}
                        style={{
                          opacity:
                            hoveredSegment !== null &&
                            hoveredSegment !== index &&
                            hoveredLegend !== index
                              ? 0.4
                              : 1,
                          transform: isHovered ? "scale(1.02)" : "scale(1)",
                          transformOrigin: "50% 50%",
                        }}
                      />
                    );
                  })}
                </svg>

                {/* Center content with smaller footprint */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div
                    className="text-center bg-white/90 backdrop-blur-sm rounded-full p-2 shadow-md border border-gray-100/50"
                    style={{ width: "90px", height: "90px" }}
                  >
                    <div className="flex flex-col justify-center h-full">
                      <div className="text-lg font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent leading-tight">
                        {(
                          topClients.reduce(
                            (sum, client) => sum + client.amount,
                            0
                          ) / 1000
                        ).toFixed(0)}
                        K DT
                      </div>
                      <div className="text-xs text-gray-500 font-medium">
                        Revenus
                      </div>
                    </div>
                  </div>
                </div>

                {/* Hover tooltip */}
                {hoveredSegment !== null && (
                  <div className="absolute top-2 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white px-3 py-2 rounded-lg text-sm font-medium shadow-lg z-10">
                    <div className="text-center">
                      <div className="font-semibold">
                        {topClients[hoveredSegment].name}
                      </div>
                      <div className="text-gray-300">
                        {topClients[hoveredSegment].amount.toLocaleString()} DT
                      </div>
                      <div className="text-gray-400">
                        {topClients[hoveredSegment].percentage}% du total
                      </div>
                    </div>
                    <div className="absolute bottom-[-4px] left-1/2 transform -translate-x-1/2 w-2 h-2 bg-gray-900 rotate-45"></div>
                  </div>
                )}
              </div>
            </div>

            {/* Enhanced Legend */}
            <div className="space-y-3">
              {topClients.map((client, index) => {
                const isHovered =
                  hoveredLegend === index || hoveredSegment === index;

                return (
                  <div
                    key={index}
                    className={`flex items-center justify-between p-4 rounded-xl transition-all duration-300 cursor-pointer ${
                      isHovered
                        ? "bg-gradient-to-r from-gray-50 to-gray-100 shadow-md transform scale-[1.02]"
                        : "hover:bg-gray-50"
                    }`}
                    onMouseEnter={() => setHoveredLegend(index)}
                    onMouseLeave={() => setHoveredLegend(null)}
                    style={{
                      opacity:
                        hoveredSegment !== null &&
                        hoveredSegment !== index &&
                        hoveredLegend !== index
                          ? 0.6
                          : 1,
                    }}
                  >
                    <div className="flex items-center space-x-4">
                      <div className="relative">
                        <div
                          className={`w-5 h-5 rounded-full shadow-sm transition-all duration-300 ${
                            isHovered ? "scale-125" : "scale-100"
                          }`}
                          style={{
                            background: `linear-gradient(135deg, ${colors[index].primary} 0%, ${colors[index].secondary} 100%)`,
                            boxShadow: isHovered
                              ? `0 0 20px ${colors[index].primary}40`
                              : "none",
                          }}
                        />
                        {isHovered && (
                          <div
                            className="absolute inset-0 rounded-full animate-pulse"
                            style={{
                              background: `linear-gradient(135deg, ${colors[index].primary} 0%, ${colors[index].secondary} 100%)`,
                              opacity: 0.3,
                            }}
                          />
                        )}
                      </div>
                      <div>
                        <div className="text-sm font-semibold text-gray-900">
                          {client.name}
                        </div>
                        <div className="text-xs text-gray-500 flex items-center space-x-2">
                          <span>{client.orders} commandes</span>
                          <span className="w-1 h-1 bg-gray-400 rounded-full"></span>
                          <span>{client.percentage}% du total</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-gray-900">
                        {client.amount.toLocaleString()} DT
                      </div>
                      <div className="text-xs text-gray-500">
                        {(
                          (client.amount /
                            topClients.reduce((sum, c) => sum + c.amount, 0)) *
                          100
                        ).toFixed(1)}
                        % part
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Summary stats */}
            <div className="mt-6 pt-6 border-t border-gray-100">
              <div className="grid grid-cols-3 gap-4 text-center">
                <div className="p-3 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg">
                  <div className="text-lg font-bold text-blue-900">
                    {topClients.reduce((sum, client) => sum + client.orders, 0)}
                  </div>
                  <div className="text-xs text-blue-600 font-medium">
                    Total Commandes
                  </div>
                </div>
                <div className="p-3 bg-gradient-to-br from-green-50 to-green-100 rounded-lg">
                  <div className="text-lg font-bold text-green-900">
                    {topClients
                      .reduce((sum, client) => sum + client.amount, 0)
                      .toLocaleString()}
                    DT
                  </div>
                  <div className="text-xs text-green-600 font-medium">
                    Revenus Totaux
                  </div>
                </div>
                <div className="p-3 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg">
                  <div className="text-lg font-bold text-purple-900">
                    {Math.round(
                      topClients.reduce(
                        (sum, client) => sum + client.amount,
                        0
                      ) / topClients.length
                    ).toLocaleString()}
                    DT
                  </div>
                  <div className="text-xs text-purple-600 font-medium">
                    Revenus Moyens
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">
              Activité Récente
            </h2>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {recentActivity.map((activity, index) => (
                <div
                  key={index}
                  className="flex items-center space-x-4 p-3 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div
                    className={`w-2 h-2 rounded-full ${
                      activity.type === "client"
                        ? "bg-blue-500"
                        : activity.type === "order"
                        ? "bg-orange-500"
                        : activity.type === "product"
                        ? "bg-green-500"
                        : activity.type === "catalogue"
                        ? "bg-purple-500"
                        : "bg-gray-500"
                    }`}
                  ></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">
                      {activity.action}
                    </p>
                    <p className="text-sm text-gray-600">{activity.entity}</p>
                  </div>
                  <span className="text-xs text-gray-500">{activity.time}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
