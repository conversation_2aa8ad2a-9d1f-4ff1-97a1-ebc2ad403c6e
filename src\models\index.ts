// ============================================================================
// MODELS INDEX - Export all models and types
// ============================================================================

// Common types and utilities
export * from './common';

// Entity models
export * from './Client';
export * from './Commercial';
export * from './Product';
export * from './Order';
export * from './Merchandizer';
export * from './Catalogue';

// Re-export commonly used types for convenience
export type {
  // Common
  BaseEntity,
  ValidationResult,
  ValidationError,
  ApiResponse,
  PaginatedResponse,
  SearchParams,
  CreateEntity,
  UpdateEntity,
  EntityWithId,
  
  // Client
  Client,
  ClientFormData,
  ClientSearchFilters,
  CreateClientData,
  UpdateClientData,
  ClientWithId,
  
  // Commercial
  Commercial,
  CommercialFormData,
  CommercialSearchFilters,
  CreateCommercialData,
  UpdateCommercialData,
  CommercialWithId,
  CommercialAccount,
  
  // Product
  Product,
  ProductFormData,
  ProductSearchFilters,
  CreateProductData,
  UpdateProductData,
  ProductWithId,
  
  // Order
  Order,
  OrderFormData,
  OrderSearchFilters,
  CreateOrderData,
  UpdateOrderData,
  OrderWithId,
  OrderLineItem,
  
  // Merchandizer
  Merchandizer,
  MerchandizerFormData,
  MerchandizerSearchFilters,
  CreateMerchandizerData,
  UpdateMerchandizerData,
  MerchandizerWithId,
  Store,
  VisitReport,
  MerchandizerAccount,
  
  // Catalogue
  Catalogue,
  CatalogueFormData,
  CatalogueSearchFilters,
  CreateCatalogueData,
  UpdateCatalogueData,
  CatalogueWithId,
  CatalogueVersion,
  CatalogueAnalytics
} from './common';

// Re-export commonly used enums
export {
  // Common enums
  EntityStatus,
  OrderStatus,
  Priority,
  
  // Client enums
  ClientType,
  ClientCategory,
  PaymentTerms,
  
  // Commercial enums
  CommercialRole,
  EmploymentType,
  TerritoryType,
  CommercialMobilePermission,
  
  // Product enums
  ProductType,
  ProductAvailability,
  ProductCondition,
  
  // Order enums
  OrderType,
  PaymentStatus,
  ShippingMethod,
  OrderSource,
  
  // Merchandizer enums
  MerchandizerType,
  VisitType,
  StoreType,
  MobilePermission,
  AccountStatus,
  
  // Catalogue enums
  CatalogueType,
  CatalogueFormat,
  DistributionChannel
} from './common';

// Re-export validation functions
export {
  // Client validation
  validateClientData,
  sanitizeClientData,
  
  // Commercial validation
  validateCommercialData,
  sanitizeCommercialData,
  
  // Product validation
  validateProductData,
  sanitizeProductData,
  
  // Order validation
  validateOrderData,
  sanitizeOrderData,
  
  // Merchandizer validation
  validateMerchandizerData,
  sanitizeMerchandizerData,
  
  // Catalogue validation
  validateCatalogueData,
  sanitizeCatalogueData
} from './common';

// Import account utilities from specific models
export {
  // Merchandizer account utilities
  canAccountLogin,
  needsPasswordChange,
  getDefaultMerchandizerPermissions,
  createDefaultAccountSettings,
  isValidUsername,
  generateTemporaryPassword,
  isDeviceRegistered,
  getActiveDevicesCount,
} from './Merchandizer';

export {
  // Commercial account utilities
  canCommercialAccountLogin,
  needsCommercialPasswordChange,
  getDefaultCommercialPermissions,
  createDefaultCommercialAccountSettings,
  canApproveDiscount,
  orderNeedsApproval,
  canAccessClient,
} from './Commercial';

// Re-export utility functions
export {
  // Common utilities
  generateId,
  formatDate,
  formatDateTime,
  isValidEmail,
  isValidPhone,
  sanitizeString,
  deepClone,
  isEmpty,
  
  // Client utilities
  getClientDisplayName,
  getClientFullAddress,
  isClientPremium,
  calculateClientLifetimeValue,
  getClientRiskLevel,
  
  // Commercial utilities
  getCommercialFullName,
  calculatePerformanceScore,
  getPerformanceRating,
  isMeetingTargets as isCommercialMeetingTargets,
  calculateCommissionEarned,
  
  // Product utilities
  calculateProfitMargin,
  isLowStock,
  isOutOfStock,
  getDisplayPrice,
  calculateVolume,
  generateProductSlug,
  needsReorder,
  getAvailabilityStatus,
  
  // Order utilities
  calculateOrderSubtotal,
  calculateOrderTotal,
  generateOrderNumber,
  canCancelOrder,
  canModifyOrder,
  getOrderStatusText,
  isOrderOverdue,
  calculateOrderProfit,
  
  // Merchandizer utilities
  getMerchandizerFullName,
  calculateMerchandizerPerformanceScore,
  getMerchandizerPerformanceRating,
  isMeetingTargets as isMerchandizerMeetingTargets,
  getTotalStoresManaged,
  getNextScheduledVisit,
  calculateAverageVisitDuration,
  
  // Catalogue utilities
  generateSlug,
  isCatalogueExpired,
  isCataloguePublished,
  getCatalogueFileExtension,
  calculateEngagementScore,
  getCataloguePerformanceRating,
  formatFileSize,
  canDownloadCatalogue
} from './common';

// Constants
export {
  DEFAULT_PAGINATION,
  VALIDATION_LIMITS,
  FILE_UPLOAD
} from './common';
