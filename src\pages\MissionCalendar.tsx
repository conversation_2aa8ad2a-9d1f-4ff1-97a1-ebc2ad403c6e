import React, { useState, useEffect } from "react";
import {
  ChevronLeft,
  ChevronRight,
  Calendar,
  Clock,
  User,
  Building,
  MapPin,
  Plus,
  Filter,
  CheckCircle,
  AlertCircle,
} from "lucide-react";
import { useApp } from "../hooks/useApp";
import { Mission } from "../firebase/services";
import { formatDate } from "../models";

const MissionCalendar: React.FC = () => {
  const { missions, merchandizers, loading } = useApp();
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [merchandizerFilter, setMerchandizerFilter] = useState("all");
  const [statusFilter, setStatusFilter] = useState("all");

  // Get current month and year
  const currentMonth = currentDate.getMonth();
  const currentYear = currentDate.getFullYear();

  // Get first day of the month and number of days
  const firstDayOfMonth = new Date(currentYear, currentMonth, 1);
  const lastDayOfMonth = new Date(currentYear, currentMonth + 1, 0);
  const daysInMonth = lastDayOfMonth.getDate();
  const startingDayOfWeek = firstDayOfMonth.getDay();

  // Month names in French
  const monthNames = [
    "Janvier",
    "Février",
    "Mars",
    "Avril",
    "Mai",
    "Juin",
    "Juillet",
    "Août",
    "Septembre",
    "Octobre",
    "Novembre",
    "Décembre",
  ];

  // Day names in French
  const dayNames = ["Dim", "Lun", "Mar", "Mer", "Jeu", "Ven", "Sam"];

  // Filter missions for the current month
  const filteredMissions = missions.filter((mission) => {
    const missionDate = mission.missionDate?.toDate();
    if (!missionDate) return false;

    const matchesMonth =
      missionDate.getMonth() === currentMonth &&
      missionDate.getFullYear() === currentYear;
    const matchesMerchandizer =
      merchandizerFilter === "all" ||
      mission.merchandizerId === merchandizerFilter;
    const matchesStatus =
      statusFilter === "all" || mission.status === statusFilter;

    return matchesMonth && matchesMerchandizer && matchesStatus;
  });

  // Get missions for a specific date
  const getMissionsForDate = (date: number) => {
    return filteredMissions.filter((mission) => {
      const missionDate = mission.missionDate?.toDate();
      return missionDate && missionDate.getDate() === date;
    });
  };

  // Navigate months
  const goToPreviousMonth = () => {
    setCurrentDate(new Date(currentYear, currentMonth - 1, 1));
  };

  const goToNextMonth = () => {
    setCurrentDate(new Date(currentYear, currentMonth + 1, 1));
  };

  const goToToday = () => {
    setCurrentDate(new Date());
  };

  // Handle date selection
  const handleDateClick = (date: number) => {
    const clickedDate = new Date(currentYear, currentMonth, date);
    setSelectedDate(clickedDate);
  };

  // Get missions for selected date
  const selectedDateMissions = selectedDate
    ? getMissionsForDate(selectedDate.getDate())
    : [];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-500";
      case "in_progress":
        return "bg-blue-500";
      case "pending":
        return "bg-yellow-500";
      case "cancelled":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "completed":
        return "Terminée";
      case "in_progress":
        return "En cours";
      case "pending":
        return "En attente";
      case "cancelled":
        return "Annulée";
      default:
        return status;
    }
  };

  // Create calendar grid
  const calendarDays = [];

  // Add empty cells for days before the first day of the month
  for (let i = 0; i < startingDayOfWeek; i++) {
    calendarDays.push(null);
  }

  // Add days of the month
  for (let day = 1; day <= daysInMonth; day++) {
    calendarDays.push(day);
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">
          Calendrier des Missions
        </h1>
        <div className="flex items-center space-x-4">
          <button
            onClick={goToToday}
            className="px-4 py-2 text-blue-600 border border-blue-600 rounded-lg hover:bg-blue-50 transition-colors"
          >
            Aujourd'hui
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <select
            value={merchandizerFilter}
            onChange={(e) => setMerchandizerFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">Tous les merchandizers</option>
            {merchandizers.map((merchandizer) => (
              <option key={merchandizer.id} value={merchandizer.id}>
                {merchandizer.name}
              </option>
            ))}
          </select>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">Tous les statuts</option>
            <option value="pending">En attente</option>
            <option value="in_progress">En cours</option>
            <option value="completed">Terminée</option>
            <option value="cancelled">Annulée</option>
          </select>
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <Filter className="h-4 w-4" />
            <span>{filteredMissions.length} mission(s) ce mois</span>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Calendar */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            {/* Calendar Header */}
            <div className="flex items-center justify-between p-4 bg-gray-50 border-b">
              <button
                onClick={goToPreviousMonth}
                className="p-2 hover:bg-gray-200 rounded-lg transition-colors"
              >
                <ChevronLeft className="h-5 w-5" />
              </button>
              <h2 className="text-lg font-semibold text-gray-900">
                {monthNames[currentMonth]} {currentYear}
              </h2>
              <button
                onClick={goToNextMonth}
                className="p-2 hover:bg-gray-200 rounded-lg transition-colors"
              >
                <ChevronRight className="h-5 w-5" />
              </button>
            </div>

            {/* Calendar Grid */}
            <div className="p-4">
              {/* Day headers */}
              <div className="grid grid-cols-7 gap-1 mb-2">
                {dayNames.map((day) => (
                  <div
                    key={day}
                    className="p-2 text-center text-sm font-medium text-gray-500"
                  >
                    {day}
                  </div>
                ))}
              </div>

              {/* Calendar days */}
              <div className="grid grid-cols-7 gap-1">
                {calendarDays.map((day, index) => {
                  if (day === null) {
                    return <div key={index} className="p-2 h-24"></div>;
                  }

                  const dayMissions = getMissionsForDate(day);
                  const isToday =
                    new Date().toDateString() ===
                    new Date(currentYear, currentMonth, day).toDateString();
                  const isSelected =
                    selectedDate?.toDateString() ===
                    new Date(currentYear, currentMonth, day).toDateString();

                  return (
                    <div
                      key={day}
                      onClick={() => handleDateClick(day)}
                      className={`p-2 h-24 border border-gray-200 cursor-pointer hover:bg-gray-50 transition-colors ${
                        isToday ? "bg-blue-50 border-blue-300" : ""
                      } ${isSelected ? "bg-blue-100 border-blue-400" : ""}`}
                    >
                      <div className="flex flex-col h-full">
                        <div
                          className={`text-sm font-medium ${
                            isToday ? "text-blue-600" : "text-gray-900"
                          }`}
                        >
                          {day}
                        </div>
                        <div className="flex-1 mt-1 space-y-1">
                          {dayMissions
                            .slice(0, 2)
                            .map((mission, missionIndex) => (
                              <div
                                key={mission.id}
                                className={`text-xs px-1 py-0.5 rounded text-white truncate ${getStatusColor(
                                  mission.status
                                )}`}
                                title={`${mission.title} - ${mission.merchandizerName}`}
                              >
                                {mission.title}
                              </div>
                            ))}
                          {dayMissions.length > 2 && (
                            <div className="text-xs text-gray-500">
                              +{dayMissions.length - 2} autres
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>

        {/* Mission Details Panel */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-4 border-b">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <Calendar className="h-5 w-5 mr-2" />
                {selectedDate
                  ? formatDate({ toDate: () => selectedDate } as any)
                  : "Sélectionnez une date"}
              </h3>
            </div>
            <div className="p-4">
              {selectedDate ? (
                selectedDateMissions.length > 0 ? (
                  <div className="space-y-4">
                    {selectedDateMissions.map((mission) => (
                      <div
                        key={mission.id}
                        className="border border-gray-200 rounded-lg p-3"
                      >
                        <div className="flex items-start justify-between mb-2">
                          <h4 className="font-medium text-gray-900">
                            {mission.title}
                          </h4>
                          <span
                            className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${
                              mission.status === "completed"
                                ? "bg-green-100 text-green-800"
                                : mission.status === "in_progress"
                                ? "bg-blue-100 text-blue-800"
                                : mission.status === "pending"
                                ? "bg-yellow-100 text-yellow-800"
                                : "bg-red-100 text-red-800"
                            }`}
                          >
                            {getStatusText(mission.status)}
                          </span>
                        </div>

                        <div className="space-y-2 text-sm text-gray-600">
                          <div className="flex items-center">
                            <Clock className="h-4 w-4 mr-2" />
                            {mission.startTime}
                            {mission.endTime && ` - ${mission.endTime}`}
                          </div>

                          <div className="flex items-center">
                            <User className="h-4 w-4 mr-2" />
                            {mission.merchandizerName}
                          </div>

                          <div className="flex items-center">
                            <Building className="h-4 w-4 mr-2" />
                            {mission.clientName}
                          </div>

                          {mission.clientAddress && (
                            <div className="flex items-center">
                              <MapPin className="h-4 w-4 mr-2" />
                              <span className="truncate">
                                {mission.clientAddress}
                              </span>
                            </div>
                          )}
                        </div>

                        {mission.description && (
                          <div className="mt-2 text-sm text-gray-700">
                            <p className="line-clamp-2">
                              {mission.description}
                            </p>
                          </div>
                        )}

                        {mission.tasks && mission.tasks.length > 0 && (
                          <div className="mt-2">
                            <p className="text-xs font-medium text-gray-500 mb-1">
                              Tâches:
                            </p>
                            <ul className="text-xs text-gray-600 space-y-1">
                              {mission.tasks.slice(0, 3).map((task, index) => (
                                <li key={index} className="flex items-center">
                                  <div className="w-1 h-1 bg-gray-400 rounded-full mr-2"></div>
                                  {task}
                                </li>
                              ))}
                              {mission.tasks.length > 3 && (
                                <li className="text-gray-500">
                                  +{mission.tasks.length - 3} autres tâches
                                </li>
                              )}
                            </ul>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">
                      Aucune mission pour cette date
                    </p>
                  </div>
                )
              ) : (
                <div className="text-center py-8">
                  <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">
                    Cliquez sur une date pour voir les missions
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {loading && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg">
            <div className="flex items-center space-x-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span>Chargement...</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MissionCalendar;
