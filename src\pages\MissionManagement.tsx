import React, { useState, useEffect } from "react";
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  Calendar,
  Clock,
  MapPin,
  User,
  Building,
  CheckCircle,
  AlertCircle,
  Filter,
  Users,
} from "lucide-react";
import { useApp } from "../hooks/useApp";
import { Mission } from "../firebase/services";
import { formatDate, formatDateTime } from "../models";
import ConfirmationModal from "../components/ui/ConfirmationModal";

const MissionManagement: React.FC = () => {
  const { missions, merchandizers, clients, loading, deleteMission } = useApp();
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [merchandizerFilter, setMerchandizerFilter] = useState("all");
  const [showModal, setShowModal] = useState(false);
  const [editingMission, setEditingMission] = useState<Mission | null>(null);
  const [modalMode, setModalMode] = useState<"create" | "edit" | "view">(
    "create"
  );
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    isOpen: boolean;
    mission: Mission | null;
    loading: boolean;
  }>({ isOpen: false, mission: null, loading: false });

  const filteredMissions = missions.filter((mission) => {
    const matchesSearch =
      mission.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      mission.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      mission.merchandizerName
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      mission.description.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus =
      statusFilter === "all" || mission.status === statusFilter;
    const matchesMerchandizer =
      merchandizerFilter === "all" ||
      mission.merchandizerId === merchandizerFilter;

    return matchesSearch && matchesStatus && matchesMerchandizer;
  });

  const handleEdit = (mission: Mission) => {
    setEditingMission(mission);
    setModalMode("edit");
    setShowModal(true);
  };

  const handleView = (mission: Mission) => {
    setEditingMission(mission);
    setModalMode("view");
    setShowModal(true);
  };

  const handleDelete = (mission: Mission) => {
    setDeleteConfirmation({ isOpen: true, mission, loading: false });
  };

  const handleConfirmDelete = async () => {
    if (!deleteConfirmation.mission?.id) return;

    setDeleteConfirmation((prev) => ({ ...prev, loading: true }));

    try {
      await deleteMission(deleteConfirmation.mission.id);
      setDeleteConfirmation({ isOpen: false, mission: null, loading: false });
    } catch (error) {
      console.error("Error deleting mission:", error);
      setDeleteConfirmation((prev) => ({ ...prev, loading: false }));
    }
  };

  const handleCancelDelete = () => {
    setDeleteConfirmation({ isOpen: false, mission: null, loading: false });
  };

  const handleAdd = () => {
    setEditingMission(null);
    setModalMode("create");
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setEditingMission(null);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "in_progress":
        return "bg-blue-100 text-blue-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4" />;
      case "in_progress":
        return <Clock className="h-4 w-4" />;
      case "pending":
        return <AlertCircle className="h-4 w-4" />;
      case "cancelled":
        return <Trash2 className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "completed":
        return "Terminée";
      case "in_progress":
        return "En cours";
      case "pending":
        return "En attente";
      case "cancelled":
        return "Annulée";
      default:
        return status;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "urgent":
        return "bg-red-500";
      case "high":
        return "bg-orange-500";
      case "medium":
        return "bg-yellow-500";
      case "low":
        return "bg-green-500";
      default:
        return "bg-gray-500";
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">
          Gestion des Missions
        </h1>
        <button
          onClick={handleAdd}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
        >
          <Plus className="h-4 w-4" />
          <span>Nouvelle Mission</span>
        </button>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Rechercher des missions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">Tous les statuts</option>
            <option value="pending">En attente</option>
            <option value="in_progress">En cours</option>
            <option value="completed">Terminée</option>
            <option value="cancelled">Annulée</option>
          </select>
          <select
            value={merchandizerFilter}
            onChange={(e) => setMerchandizerFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">Tous les merchandizers</option>
            {merchandizers.map((merchandizer) => (
              <option key={merchandizer.id} value={merchandizer.id}>
                {merchandizer.name}
              </option>
            ))}
          </select>
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <Filter className="h-4 w-4" />
            <span>{filteredMissions.length} mission(s)</span>
          </div>
        </div>
      </div>

      {/* Missions Table */}
      <div className="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Mission
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Merchandizer
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Client
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Date & Heure
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Statut
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Priorité
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {filteredMissions.length === 0 ? (
              <tr>
                <td colSpan={7} className="px-6 py-12 text-center">
                  <div className="flex flex-col items-center">
                    <Users className="h-12 w-12 text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Pas de missions
                    </h3>
                    <p className="text-gray-500 mb-4">
                      {searchTerm ||
                      statusFilter !== "all" ||
                      merchandizerFilter !== "all"
                        ? "Aucune mission ne correspond à vos critères de recherche."
                        : "Commencez par créer votre première mission."}
                    </p>
                    {!searchTerm &&
                      statusFilter === "all" &&
                      merchandizerFilter === "all" && (
                        <button
                          onClick={handleAdd}
                          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
                        >
                          <Plus className="h-4 w-4" />
                          <span>Créer une Mission</span>
                        </button>
                      )}
                  </div>
                </td>
              </tr>
            ) : (
              filteredMissions.map((mission) => (
                <tr key={mission.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {mission.title}
                      </div>
                      <div className="text-sm text-gray-500 truncate max-w-xs">
                        {mission.description}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <User className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="text-sm text-gray-900">
                        {mission.merchandizerName}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Building className="h-4 w-4 text-gray-400 mr-2" />
                      <div>
                        <div className="text-sm text-gray-900">
                          {mission.clientName}
                        </div>
                        {mission.clientAddress && (
                          <div className="text-xs text-gray-500 flex items-center">
                            <MapPin className="h-3 w-3 mr-1" />
                            {mission.clientAddress}
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                        {mission.missionDate
                          ? formatDate(mission.missionDate)
                          : "N/A"}
                      </div>
                      <div className="flex items-center text-gray-500">
                        <Clock className="h-4 w-4 text-gray-400 mr-2" />
                        {mission.startTime}
                        {mission.endTime && ` - ${mission.endTime}`}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(
                        mission.status
                      )}`}
                    >
                      <span className="mr-1">
                        {getStatusIcon(mission.status)}
                      </span>
                      {getStatusText(mission.status)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div
                        className={`w-3 h-3 rounded-full ${getPriorityColor(
                          mission.priority
                        )} mr-2`}
                      ></div>
                      <span className="text-sm text-gray-900 capitalize">
                        {mission.priority}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleView(mission)}
                        className="text-gray-600 hover:text-gray-900 transition-colors"
                        title="Voir"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleEdit(mission)}
                        className="text-blue-600 hover:text-blue-900 transition-colors"
                        title="Modifier"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDelete(mission)}
                        className="text-red-600 hover:text-red-900 transition-colors"
                        title="Supprimer"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Mission Modal - TODO: Create MissionModal component */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg max-w-md w-full">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              {modalMode === "create"
                ? "Nouvelle Mission"
                : modalMode === "edit"
                ? "Modifier Mission"
                : "Détails Mission"}
            </h3>
            <p className="text-gray-500 mb-4">
              Le modal de mission sera implémenté dans la prochaine étape.
            </p>
            <div className="flex justify-end">
              <button
                onClick={handleCloseModal}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Fermer
              </button>
            </div>
          </div>
        </div>
      )}

      <ConfirmationModal
        isOpen={deleteConfirmation.isOpen}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title="Supprimer la mission"
        message={`Êtes-vous sûr de vouloir supprimer la mission "${deleteConfirmation.mission?.title}" ? Cette action est irréversible.`}
        confirmText="Supprimer"
        cancelText="Annuler"
        type="danger"
        loading={deleteConfirmation.loading}
      />

      {loading && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg">
            <div className="flex items-center space-x-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span>Chargement...</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MissionManagement;
