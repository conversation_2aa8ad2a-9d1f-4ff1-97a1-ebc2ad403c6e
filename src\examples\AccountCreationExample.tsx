import React, { useState } from "react";
import {
  Eye,
  EyeOff,
  RefreshCw,
  User,
  Smartphone,
  Shield,
  Clock,
} from "lucide-react";
import {
  MobilePermission,
  CommercialMobilePermission,
  AccountStatus,
  generateTemporaryPassword,
  getDefaultMerchandizerPermissions,
  getDefaultCommercialPermissions,
  isValidUsername,
  createDefaultAccountSettings,
  createDefaultCommercialAccountSettings,
} from "../models";

const AccountCreationExample: React.FC = () => {
  const [accountType, setAccountType] = useState<"merchandizer" | "commercial">(
    "merchandizer"
  );
  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState({
    // Basic info
    firstName: "",
    lastName: "",
    email: "",

    // Account credentials
    username: "",
    temporaryPassword: generateTemporaryPassword(),

    // Permissions
    merchandizerPermissions: getDefaultMerchandizerPermissions(),
    commercialPermissions: getDefaultCommercialPermissions(),

    // Security settings
    canAccessOffline: true,
    maxOfflineDays: 7,
    requireBiometric: false,
    sessionTimeout: 480, // 8 hours
    allowMultipleDevices: false,

    // Commercial specific
    canViewAllClients: false,
    canEditPricing: false,
    maxDiscountPercentage: 5,
    requireApprovalAbove: 10000,
  });

  // Auto-generate username from name
  const generateUsername = (firstName: string, lastName: string) => {
    const combined = `${firstName}${lastName}`;
    return combined
      .toLowerCase()
      .replace(/[^a-z0-9]/g, "")
      .substring(0, 15);
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => {
      const updated = { ...prev, [field]: value };

      // Auto-generate username when name changes
      if (field === "firstName" || field === "lastName") {
        updated.username = generateUsername(
          field === "firstName" ? value : prev.firstName,
          field === "lastName" ? value : prev.lastName
        );
      }

      return updated;
    });
  };

  const handlePermissionToggle = (permission: string) => {
    const permissionField =
      accountType === "merchandizer"
        ? "merchandizerPermissions"
        : "commercialPermissions";

    setFormData((prev) => ({
      ...prev,
      [permissionField]: prev[permissionField].includes(permission)
        ? prev[permissionField].filter((p: string) => p !== permission)
        : [...prev[permissionField], permission],
    }));
  };

  const handleGeneratePassword = () => {
    setFormData((prev) => ({
      ...prev,
      temporaryPassword: generateTemporaryPassword(),
    }));
  };

  const handleCreateAccount = () => {
    // Validate form
    if (!formData.firstName.trim() || !formData.lastName.trim()) {
      alert("Prénom et nom sont requis");
      return;
    }

    if (!formData.email.trim() || !formData.email.includes("@")) {
      alert("Email valide requis");
      return;
    }

    if (!isValidUsername(formData.username)) {
      alert(
        "Nom d'utilisateur invalide (3-20 caractères, lettres/chiffres/underscore uniquement)"
      );
      return;
    }

    if (formData.temporaryPassword.length < 6) {
      alert("Le mot de passe doit contenir au moins 6 caractères");
      return;
    }

    // Create account object
    const accountData =
      accountType === "merchandizer"
        ? createDefaultAccountSettings(
            formData.username,
            formData.email,
            formData.temporaryPassword,
            "admin" // createdBy
          )
        : createDefaultCommercialAccountSettings(
            formData.username,
            formData.email,
            formData.temporaryPassword,
            "admin" // createdBy
          );

    // Update with form settings
    accountData.permissions =
      accountType === "merchandizer"
        ? formData.merchandizerPermissions
        : formData.commercialPermissions;
    accountData.canAccessOffline = formData.canAccessOffline;
    accountData.maxOfflineDays = formData.maxOfflineDays;
    accountData.requireBiometric = formData.requireBiometric;
    accountData.sessionTimeout = formData.sessionTimeout;
    accountData.allowMultipleDevices = formData.allowMultipleDevices;

    if (accountType === "commercial") {
      (accountData as any).canViewAllClients = formData.canViewAllClients;
      (accountData as any).canEditPricing = formData.canEditPricing;
      (accountData as any).maxDiscountPercentage =
        formData.maxDiscountPercentage;
      (accountData as any).requireApprovalAbove = formData.requireApprovalAbove;
    }

    console.log("Account created:", accountData);
    alert(
      `Compte ${accountType} créé avec succès!\nNom d'utilisateur: ${formData.username}\nMot de passe temporaire: ${formData.temporaryPassword}`
    );
  };

  const availablePermissions =
    accountType === "merchandizer"
      ? Object.values(MobilePermission)
      : Object.values(CommercialMobilePermission);

  const currentPermissions =
    accountType === "merchandizer"
      ? formData.merchandizerPermissions
      : formData.commercialPermissions;

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h1 className="text-2xl font-bold text-gray-900 mb-6">
        Création de Compte Mobile App
      </h1>

      {/* Account Type Selection */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Type de Compte
        </label>
        <div className="flex space-x-4">
          <button
            onClick={() => setAccountType("merchandizer")}
            className={`px-4 py-2 rounded-md ${
              accountType === "merchandizer"
                ? "bg-blue-600 text-white"
                : "bg-gray-200 text-gray-700 hover:bg-gray-300"
            }`}
          >
            <User className="inline h-4 w-4 mr-2" />
            Merchandizer
          </button>
          <button
            onClick={() => setAccountType("commercial")}
            className={`px-4 py-2 rounded-md ${
              accountType === "commercial"
                ? "bg-blue-600 text-white"
                : "bg-gray-200 text-gray-700 hover:bg-gray-300"
            }`}
          >
            <User className="inline h-4 w-4 mr-2" />
            Commercial
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Basic Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900">
            Informations de Base
          </h3>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Prénom *
            </label>
            <input
              type="text"
              value={formData.firstName}
              onChange={(e) => handleInputChange("firstName", e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Jean"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Nom *
            </label>
            <input
              type="text"
              value={formData.lastName}
              onChange={(e) => handleInputChange("lastName", e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Dupont"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Email *
            </label>
            <input
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange("email", e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="<EMAIL>"
            />
          </div>

          {/* Account Credentials */}
          <h3 className="text-lg font-semibold text-gray-900 mt-6">
            Identifiants de Connexion
          </h3>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Nom d'utilisateur *
            </label>
            <input
              type="text"
              value={formData.username}
              onChange={(e) => handleInputChange("username", e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="jeandupont"
            />
            <p className="text-xs text-gray-500 mt-1">
              3-20 caractères, lettres, chiffres et underscore uniquement
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Mot de passe temporaire *
            </label>
            <div className="relative">
              <input
                type={showPassword ? "text" : "password"}
                value={formData.temporaryPassword}
                onChange={(e) =>
                  handleInputChange("temporaryPassword", e.target.value)
                }
                className="w-full px-3 py-2 pr-20 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <div className="absolute inset-y-0 right-0 flex items-center space-x-1 pr-3">
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </button>
                <button
                  type="button"
                  onClick={handleGeneratePassword}
                  className="text-blue-600 hover:text-blue-800"
                  title="Générer un nouveau mot de passe"
                >
                  <RefreshCw className="h-4 w-4" />
                </button>
              </div>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              L'utilisateur devra changer ce mot de passe lors de sa première
              connexion
            </p>
          </div>
        </div>

        {/* Permissions and Settings */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900">
            Permissions Mobile
          </h3>

          <div className="space-y-2 max-h-48 overflow-y-auto border border-gray-200 rounded-md p-3">
            {availablePermissions.map((permission) => (
              <label key={permission} className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={currentPermissions.includes(permission)}
                  onChange={() => handlePermissionToggle(permission)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700">
                  {permission.replace(/_/g, " ").toLowerCase()}
                </span>
              </label>
            ))}
          </div>

          <h3 className="text-lg font-semibold text-gray-900 mt-6">
            Paramètres de Sécurité
          </h3>

          <div className="space-y-3">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={formData.canAccessOffline}
                onChange={(e) =>
                  handleInputChange("canAccessOffline", e.target.checked)
                }
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <Smartphone className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-700">
                Accès hors ligne autorisé
              </span>
            </label>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Jours hors ligne maximum
              </label>
              <input
                type="number"
                min="1"
                max="30"
                value={formData.maxOfflineDays}
                onChange={(e) =>
                  handleInputChange("maxOfflineDays", parseInt(e.target.value))
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={formData.requireBiometric}
                onChange={(e) =>
                  handleInputChange("requireBiometric", e.target.checked)
                }
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <Shield className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-700">
                Authentification biométrique requise
              </span>
            </label>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                <Clock className="inline h-4 w-4 mr-1" />
                Timeout de session (minutes)
              </label>
              <input
                type="number"
                min="30"
                max="1440"
                step="30"
                value={formData.sessionTimeout}
                onChange={(e) =>
                  handleInputChange("sessionTimeout", parseInt(e.target.value))
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={formData.allowMultipleDevices}
                onChange={(e) =>
                  handleInputChange("allowMultipleDevices", e.target.checked)
                }
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">
                Autoriser plusieurs appareils
              </span>
            </label>
          </div>

          {/* Commercial Specific Settings */}
          {accountType === "commercial" && (
            <>
              <h3 className="text-lg font-semibold text-gray-900 mt-6">
                Paramètres Commercial
              </h3>

              <div className="space-y-3">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={formData.canViewAllClients}
                    onChange={(e) =>
                      handleInputChange("canViewAllClients", e.target.checked)
                    }
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">
                    Voir tous les clients
                  </span>
                </label>

                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={formData.canEditPricing}
                    onChange={(e) =>
                      handleInputChange("canEditPricing", e.target.checked)
                    }
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">
                    Modifier les prix
                  </span>
                </label>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Remise maximum autorisée (%)
                  </label>
                  <input
                    type="number"
                    min="0"
                    max="100"
                    value={formData.maxDiscountPercentage}
                    onChange={(e) =>
                      handleInputChange(
                        "maxDiscountPercentage",
                        parseFloat(e.target.value)
                      )
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Approbation requise au-dessus de (DT)
                  </label>
                  <input
                    type="number"
                    min="0"
                    value={formData.requireApprovalAbove}
                    onChange={(e) =>
                      handleInputChange(
                        "requireApprovalAbove",
                        parseFloat(e.target.value)
                      )
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Create Account Button */}
      <div className="mt-8 flex justify-end">
        <button
          onClick={handleCreateAccount}
          className="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          Créer le Compte{" "}
          {accountType === "merchandizer" ? "Merchandizer" : "Commercial"}
        </button>
      </div>
    </div>
  );
};

export default AccountCreationExample;
